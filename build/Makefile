# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Users/<USER>/.local/homebrew/bin/ctest $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Users/<USER>/.local/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Users/<USER>/.local/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.local/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/Users/<USER>/.local/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/Users/<USER>/.local/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles /Users/<USER>/Desktop/ltst/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named MacOS3DLooterShooter

# Build rule for target.
MacOS3DLooterShooter: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 MacOS3DLooterShooter
.PHONY : MacOS3DLooterShooter

# fast build rule for target.
MacOS3DLooterShooter/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/build
.PHONY : MacOS3DLooterShooter/fast

#=============================================================================
# Target rules for targets named compile_shaders

# Build rule for target.
compile_shaders: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 compile_shaders
.PHONY : compile_shaders

# fast build rule for target.
compile_shaders/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/compile_shaders.dir/build.make CMakeFiles/compile_shaders.dir/build
.PHONY : compile_shaders/fast

#=============================================================================
# Target rules for targets named copy_assets

# Build rule for target.
copy_assets: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 copy_assets
.PHONY : copy_assets

# fast build rule for target.
copy_assets/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_assets.dir/build.make CMakeFiles/copy_assets.dir/build
.PHONY : copy_assets/fast

#=============================================================================
# Target rules for targets named game_tests

# Build rule for target.
game_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 game_tests
.PHONY : game_tests

# fast build rule for target.
game_tests/fast:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/build
.PHONY : game_tests/fast

src/assets/AssetManager.o: src/assets/AssetManager.cpp.o
.PHONY : src/assets/AssetManager.o

# target to build an object file
src/assets/AssetManager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o
.PHONY : src/assets/AssetManager.cpp.o

src/assets/AssetManager.i: src/assets/AssetManager.cpp.i
.PHONY : src/assets/AssetManager.i

# target to preprocess a source file
src/assets/AssetManager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.i
.PHONY : src/assets/AssetManager.cpp.i

src/assets/AssetManager.s: src/assets/AssetManager.cpp.s
.PHONY : src/assets/AssetManager.s

# target to generate assembly for a file
src/assets/AssetManager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.s
.PHONY : src/assets/AssetManager.cpp.s

src/assets/ModelAsset.o: src/assets/ModelAsset.cpp.o
.PHONY : src/assets/ModelAsset.o

# target to build an object file
src/assets/ModelAsset.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o
.PHONY : src/assets/ModelAsset.cpp.o

src/assets/ModelAsset.i: src/assets/ModelAsset.cpp.i
.PHONY : src/assets/ModelAsset.i

# target to preprocess a source file
src/assets/ModelAsset.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.i
.PHONY : src/assets/ModelAsset.cpp.i

src/assets/ModelAsset.s: src/assets/ModelAsset.cpp.s
.PHONY : src/assets/ModelAsset.s

# target to generate assembly for a file
src/assets/ModelAsset.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.s
.PHONY : src/assets/ModelAsset.cpp.s

src/assets/TextureAsset.o: src/assets/TextureAsset.cpp.o
.PHONY : src/assets/TextureAsset.o

# target to build an object file
src/assets/TextureAsset.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o
.PHONY : src/assets/TextureAsset.cpp.o

src/assets/TextureAsset.i: src/assets/TextureAsset.cpp.i
.PHONY : src/assets/TextureAsset.i

# target to preprocess a source file
src/assets/TextureAsset.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.i
.PHONY : src/assets/TextureAsset.cpp.i

src/assets/TextureAsset.s: src/assets/TextureAsset.cpp.s
.PHONY : src/assets/TextureAsset.s

# target to generate assembly for a file
src/assets/TextureAsset.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.s
.PHONY : src/assets/TextureAsset.cpp.s

src/camera/CameraSystem.o: src/camera/CameraSystem.cpp.o
.PHONY : src/camera/CameraSystem.o

# target to build an object file
src/camera/CameraSystem.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o
.PHONY : src/camera/CameraSystem.cpp.o

src/camera/CameraSystem.i: src/camera/CameraSystem.cpp.i
.PHONY : src/camera/CameraSystem.i

# target to preprocess a source file
src/camera/CameraSystem.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.i
.PHONY : src/camera/CameraSystem.cpp.i

src/camera/CameraSystem.s: src/camera/CameraSystem.cpp.s
.PHONY : src/camera/CameraSystem.s

# target to generate assembly for a file
src/camera/CameraSystem.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.s
.PHONY : src/camera/CameraSystem.cpp.s

src/engine/Engine.o: src/engine/Engine.cpp.o
.PHONY : src/engine/Engine.o

# target to build an object file
src/engine/Engine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o
.PHONY : src/engine/Engine.cpp.o

src/engine/Engine.i: src/engine/Engine.cpp.i
.PHONY : src/engine/Engine.i

# target to preprocess a source file
src/engine/Engine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.i
.PHONY : src/engine/Engine.cpp.i

src/engine/Engine.s: src/engine/Engine.cpp.s
.PHONY : src/engine/Engine.s

# target to generate assembly for a file
src/engine/Engine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.s
.PHONY : src/engine/Engine.cpp.s

src/graphics/MetalDevice.o: src/graphics/MetalDevice.mm.o
.PHONY : src/graphics/MetalDevice.o

# target to build an object file
src/graphics/MetalDevice.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/MetalDevice.mm.o
.PHONY : src/graphics/MetalDevice.mm.o

src/graphics/Renderer3D.o: src/graphics/Renderer3D.mm.o
.PHONY : src/graphics/Renderer3D.o

# target to build an object file
src/graphics/Renderer3D.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/Renderer3D.mm.o
.PHONY : src/graphics/Renderer3D.mm.o

src/graphics/SimpleRenderSystem.o: src/graphics/SimpleRenderSystem.cpp.o
.PHONY : src/graphics/SimpleRenderSystem.o

# target to build an object file
src/graphics/SimpleRenderSystem.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o
.PHONY : src/graphics/SimpleRenderSystem.cpp.o

src/graphics/SimpleRenderSystem.i: src/graphics/SimpleRenderSystem.cpp.i
.PHONY : src/graphics/SimpleRenderSystem.i

# target to preprocess a source file
src/graphics/SimpleRenderSystem.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.i
.PHONY : src/graphics/SimpleRenderSystem.cpp.i

src/graphics/SimpleRenderSystem.s: src/graphics/SimpleRenderSystem.cpp.s
.PHONY : src/graphics/SimpleRenderSystem.s

# target to generate assembly for a file
src/graphics/SimpleRenderSystem.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.s
.PHONY : src/graphics/SimpleRenderSystem.cpp.s

src/graphics/TextureGenerator.o: src/graphics/TextureGenerator.mm.o
.PHONY : src/graphics/TextureGenerator.o

# target to build an object file
src/graphics/TextureGenerator.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/TextureGenerator.mm.o
.PHONY : src/graphics/TextureGenerator.mm.o

src/graphics/WindowManager.o: src/graphics/WindowManager.mm.o
.PHONY : src/graphics/WindowManager.o

# target to build an object file
src/graphics/WindowManager.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/WindowManager.mm.o
.PHONY : src/graphics/WindowManager.mm.o

src/input/CocoaInputHandler.o: src/input/CocoaInputHandler.mm.o
.PHONY : src/input/CocoaInputHandler.o

# target to build an object file
src/input/CocoaInputHandler.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o
.PHONY : src/input/CocoaInputHandler.mm.o

src/input/InputSystem.o: src/input/InputSystem.cpp.o
.PHONY : src/input/InputSystem.o

# target to build an object file
src/input/InputSystem.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o
.PHONY : src/input/InputSystem.cpp.o

src/input/InputSystem.i: src/input/InputSystem.cpp.i
.PHONY : src/input/InputSystem.i

# target to preprocess a source file
src/input/InputSystem.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.i
.PHONY : src/input/InputSystem.cpp.i

src/input/InputSystem.s: src/input/InputSystem.cpp.s
.PHONY : src/input/InputSystem.s

# target to generate assembly for a file
src/input/InputSystem.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.s
.PHONY : src/input/InputSystem.cpp.s

src/main.o: src/main.mm.o
.PHONY : src/main.o

# target to build an object file
src/main.mm.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/src/main.mm.o
.PHONY : src/main.mm.o

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... compile_shaders"
	@echo "... copy_assets"
	@echo "... MacOS3DLooterShooter"
	@echo "... game_tests"
	@echo "... src/assets/AssetManager.o"
	@echo "... src/assets/AssetManager.i"
	@echo "... src/assets/AssetManager.s"
	@echo "... src/assets/ModelAsset.o"
	@echo "... src/assets/ModelAsset.i"
	@echo "... src/assets/ModelAsset.s"
	@echo "... src/assets/TextureAsset.o"
	@echo "... src/assets/TextureAsset.i"
	@echo "... src/assets/TextureAsset.s"
	@echo "... src/camera/CameraSystem.o"
	@echo "... src/camera/CameraSystem.i"
	@echo "... src/camera/CameraSystem.s"
	@echo "... src/engine/Engine.o"
	@echo "... src/engine/Engine.i"
	@echo "... src/engine/Engine.s"
	@echo "... src/graphics/MetalDevice.o"
	@echo "... src/graphics/Renderer3D.o"
	@echo "... src/graphics/SimpleRenderSystem.o"
	@echo "... src/graphics/SimpleRenderSystem.i"
	@echo "... src/graphics/SimpleRenderSystem.s"
	@echo "... src/graphics/TextureGenerator.o"
	@echo "... src/graphics/WindowManager.o"
	@echo "... src/input/CocoaInputHandler.o"
	@echo "... src/input/InputSystem.o"
	@echo "... src/input/InputSystem.i"
	@echo "... src/input/InputSystem.s"
	@echo "... src/main.o"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

