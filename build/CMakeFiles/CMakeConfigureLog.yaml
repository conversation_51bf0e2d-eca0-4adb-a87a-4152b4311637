
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Darwin - 22.3.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/Desktop/ltst/build/CMakeFiles/4.0.3/CompilerIdCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler apple sysroot: "/usr/bin/c++" "-E" "apple-sdk.cpp"
        # 1 "apple-sdk.cpp"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 428 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.cpp" 2
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 243 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 244 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.cpp" 2
        
        
      Found apple sysroot: /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineOBJCXXCompiler.cmake:124 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the OBJCXX compiler identification source file "CMakeOBJCXXCompilerId.mm" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the OBJCXX compiler identification source "CMakeOBJCXXCompilerId.mm" produced "a.out"
      
      The OBJCXX compiler identification is AppleClang, found in:
        /Users/<USER>/Desktop/ltst/build/CMakeFiles/4.0.3/CompilerIdOBJCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineOBJCXXCompiler.cmake:124 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting OBJCXX compiler apple sysroot: "/usr/bin/c++" "-E" "apple-sdk.mm"
        # 1 "apple-sdk.mm"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 439 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.mm" 2
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 243 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 244 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.mm" 2
        
        
      Found apple sysroot: /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd"
      binary: "/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd'
        
        Run Build Command(s): /Users/<USER>/.local/homebrew/bin/cmake -E env VERBOSE=1 /Users/<USER>/.local/homebrew/bin/gmake -f Makefile cmTC_cbf0f/fast
        /Users/<USER>/.local/homebrew/bin/gmake  -f CMakeFiles/cmTC_cbf0f.dir/build.make CMakeFiles/cmTC_cbf0f.dir/build
        gmake[1]: Entering directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd'
        Building CXX object CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -c /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 14.0.3 (clang-1403.*********)
        Target: arm64-apple-darwin22.3.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx13.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=13.3 -fvisibility-inlines-hidden-static-local-var -target-cpu apple-m1 -target-feature +v8.5a -target-feature +crc -target-feature +lse -target-feature +rdm -target-feature +crypto -target-feature +dotprod -target-feature +fp-armv8 -target-feature +neon -target-feature +fp16fml -target-feature +ras -target-feature +rcpc -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-feature +sm4 -target-feature +sha3 -target-feature +sha2 -target-feature +aes -target-abi darwinpcs -fallow-half-arguments-and-returns -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=lldb -target-linker-version 857.1 -v -fcoverage-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3 -dependency-file CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -no-opaque-pointers -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -x c++ /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 14.0.3 (clang-1403.*********) default target arm64-apple-darwin22.3.0
        ignoring nonexistent directory "/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1
         /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_cbf0f
        /Users/<USER>/.local/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cbf0f.dir/link.txt --verbose=1
        Apple clang version 14.0.3 (clang-1403.*********)
        Target: arm64-apple-darwin22.3.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 13.0.0 13.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -o cmTC_cbf0f -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-857.1
        BUILD 23:13:29 May  7 2023
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/
        /usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_cbf0f
        gmake[1]: Leaving directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1;/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd']
        ignore line: []
        ignore line: [Run Build Command(s): /Users/<USER>/.local/homebrew/bin/cmake -E env VERBOSE=1 /Users/<USER>/.local/homebrew/bin/gmake -f Makefile cmTC_cbf0f/fast]
        ignore line: [/Users/<USER>/.local/homebrew/bin/gmake  -f CMakeFiles/cmTC_cbf0f.dir/build.make CMakeFiles/cmTC_cbf0f.dir/build]
        ignore line: [gmake[1]: Entering directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd']
        ignore line: [Building CXX object CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -c /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 14.0.3 (clang-1403.*********)]
        ignore line: [Target: arm64-apple-darwin22.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx13.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=13.3 -fvisibility-inlines-hidden-static-local-var -target-cpu apple-m1 -target-feature +v8.5a -target-feature +crc -target-feature +lse -target-feature +rdm -target-feature +crypto -target-feature +dotprod -target-feature +fp-armv8 -target-feature +neon -target-feature +fp16fml -target-feature +ras -target-feature +rcpc -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-feature +sm4 -target-feature +sha3 -target-feature +sha2 -target-feature +aes -target-abi darwinpcs -fallow-half-arguments-and-returns -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=lldb -target-linker-version 857.1 -v -fcoverage-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3 -dependency-file CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-vC5LJd -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -no-opaque-pointers -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -x c++ /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 14.0.3 (clang-1403.*********) default target arm64-apple-darwin22.3.0]
        ignore line: [ignoring nonexistent directory "/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_cbf0f]
        ignore line: [/Users/<USER>/.local/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cbf0f.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 14.0.3 (clang-1403.*********)]
        ignore line: [Target: arm64-apple-darwin22.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 13.0.0 13.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -o cmTC_cbf0f -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [13.0.0] ==> ignore
          arg [13.3] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_cbf0f] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_cbf0f.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-857.1
      BUILD 23:13:29 May  7 2023
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.3, (clang-1403.*********) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.3 (tapi-1403.0.5.1)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestOBJCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting OBJCXX compiler ABI info"
    directories:
      source: "/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe"
      binary: "/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe"
    cmakeVariables:
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OBJCXX_FLAGS: ""
      CMAKE_OBJCXX_FLAGS_DEBUG: "-g"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_OBJCXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe'
        
        Run Build Command(s): /Users/<USER>/.local/homebrew/bin/cmake -E env VERBOSE=1 /Users/<USER>/.local/homebrew/bin/gmake -f Makefile cmTC_2ae50/fast
        /Users/<USER>/.local/homebrew/bin/gmake  -f CMakeFiles/cmTC_2ae50.dir/build.make CMakeFiles/cmTC_2ae50.dir/build
        gmake[1]: Entering directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe'
        Building OBJCXX object CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o
        /usr/bin/c++   -x objective-c++ -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -MF CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o.d -o CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -c /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeOBJCXXCompilerABI.mm
        Apple clang version 14.0.3 (clang-1403.*********)
        Target: arm64-apple-darwin22.3.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx13.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeOBJCXXCompilerABI.mm -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=13.3 -fvisibility-inlines-hidden-static-local-var -target-cpu apple-m1 -target-feature +v8.5a -target-feature +crc -target-feature +lse -target-feature +rdm -target-feature +crypto -target-feature +dotprod -target-feature +fp-armv8 -target-feature +neon -target-feature +fp16fml -target-feature +ras -target-feature +rcpc -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-feature +sm4 -target-feature +sha3 -target-feature +sha2 -target-feature +aes -target-abi darwinpcs -fallow-half-arguments-and-returns -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=lldb -target-linker-version 857.1 -v -fcoverage-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3 -dependency-file CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -no-opaque-pointers -fobjc-runtime=macosx-13.0.0 -fobjc-exceptions -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -x objective-c++ /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeOBJCXXCompilerABI.mm
        clang -cc1 version 14.0.3 (clang-1403.*********) default target arm64-apple-darwin22.3.0
        ignoring nonexistent directory "/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1
         /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking OBJCXX executable cmTC_2ae50
        /Users/<USER>/.local/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2ae50.dir/link.txt --verbose=1
        Apple clang version 14.0.3 (clang-1403.*********)
        Target: arm64-apple-darwin22.3.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 13.0.0 13.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -o cmTC_2ae50 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-857.1
        BUILD 23:13:29 May  7 2023
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/
        /usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -o cmTC_2ae50
        gmake[1]: Leaving directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestOBJCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestOBJCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed OBJCXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1;/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestOBJCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed OBJCXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe']
        ignore line: []
        ignore line: [Run Build Command(s): /Users/<USER>/.local/homebrew/bin/cmake -E env VERBOSE=1 /Users/<USER>/.local/homebrew/bin/gmake -f Makefile cmTC_2ae50/fast]
        ignore line: [/Users/<USER>/.local/homebrew/bin/gmake  -f CMakeFiles/cmTC_2ae50.dir/build.make CMakeFiles/cmTC_2ae50.dir/build]
        ignore line: [gmake[1]: Entering directory '/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe']
        ignore line: [Building OBJCXX object CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o]
        ignore line: [/usr/bin/c++   -x objective-c++ -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -MF CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o.d -o CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -c /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeOBJCXXCompilerABI.mm]
        ignore line: [Apple clang version 14.0.3 (clang-1403.*********)]
        ignore line: [Target: arm64-apple-darwin22.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx13.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeOBJCXXCompilerABI.mm -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=13.3 -fvisibility-inlines-hidden-static-local-var -target-cpu apple-m1 -target-feature +v8.5a -target-feature +crc -target-feature +lse -target-feature +rdm -target-feature +crypto -target-feature +dotprod -target-feature +fp-armv8 -target-feature +neon -target-feature +fp16fml -target-feature +ras -target-feature +rcpc -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-feature +sm4 -target-feature +sha3 -target-feature +sha2 -target-feature +aes -target-abi darwinpcs -fallow-half-arguments-and-returns -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=lldb -target-linker-version 857.1 -v -fcoverage-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3 -dependency-file CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles/CMakeScratch/TryCompile-NBaEKe -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -no-opaque-pointers -fobjc-runtime=macosx-13.0.0 -fobjc-exceptions -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -x objective-c++ /Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeOBJCXXCompilerABI.mm]
        ignore line: [clang -cc1 version 14.0.3 (clang-1403.*********) default target arm64-apple-darwin22.3.0]
        ignore line: [ignoring nonexistent directory "/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking OBJCXX executable cmTC_2ae50]
        ignore line: [/Users/<USER>/.local/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2ae50.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 14.0.3 (clang-1403.*********)]
        ignore line: [Target: arm64-apple-darwin22.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 13.0.0 13.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -o cmTC_2ae50 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [13.0.0] ==> ignore
          arg [13.3] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_2ae50] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_2ae50.dir/CMakeOBJCXXCompilerABI.mm.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
        linker tool for 'OBJCXX': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/14.0.3/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeTestOBJCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the OBJCXX compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-857.1
      BUILD 23:13:29 May  7 2023
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.3, (clang-1403.*********) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.3 (tapi-1403.0.5.1)
...
