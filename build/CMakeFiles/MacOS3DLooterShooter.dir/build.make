# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

# Include any dependencies generated for this target.
include CMakeFiles/MacOS3DLooterShooter.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/MacOS3DLooterShooter.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MacOS3DLooterShooter.dir/flags.make

CMakeFiles/MacOS3DLooterShooter.dir/codegen:
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/codegen

CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o: /Users/<USER>/Desktop/ltst/src/main.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o -c /Users/<USER>/Desktop/ltst/src/main.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/main.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/main.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o: /Users/<USER>/Desktop/ltst/src/engine/Engine.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o -c /Users/<USER>/Desktop/ltst/src/engine/Engine.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/engine/Engine.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/engine/Engine.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o: /Users/<USER>/Desktop/ltst/src/graphics/SimpleRenderSystem.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o -c /Users/<USER>/Desktop/ltst/src/graphics/SimpleRenderSystem.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/graphics/SimpleRenderSystem.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/graphics/SimpleRenderSystem.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/AssetManager.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/ModelAsset.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o: /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o -c /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/assets/TextureAsset.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o: /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm
CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building OBJCXX object CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o"
	/usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) -x objective-c++ $(OBJCXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o -c /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm

CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing OBJCXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.i"
	/usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) $(OBJCXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm > CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.i

CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling OBJCXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.s"
	/usr/bin/c++ $(OBJCXX_DEFINES) $(OBJCXX_INCLUDES) $(OBJCXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/input/CocoaInputHandler.mm -o CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.s

CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o: /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o -c /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/input/InputSystem.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.s

CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/flags.make
CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o: /Users/<USER>/Desktop/ltst/src/camera/CameraSystem.cpp
CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o: CMakeFiles/MacOS3DLooterShooter.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o -MF CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o.d -o CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o -c /Users/<USER>/Desktop/ltst/src/camera/CameraSystem.cpp

CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Desktop/ltst/src/camera/CameraSystem.cpp > CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.i

CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Desktop/ltst/src/camera/CameraSystem.cpp -o CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.s

# Object files for target MacOS3DLooterShooter
MacOS3DLooterShooter_OBJECTS = \
"CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o" \
"CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o"

# External object files for target MacOS3DLooterShooter
MacOS3DLooterShooter_EXTERNAL_OBJECTS =

MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/build.make
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Linking CXX executable MacOS3DLooterShooter"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/MacOS3DLooterShooter.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/MacOS3DLooterShooter.dir/build: MacOS3DLooterShooter
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/build

CMakeFiles/MacOS3DLooterShooter.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MacOS3DLooterShooter.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/clean

CMakeFiles/MacOS3DLooterShooter.dir/depend:
	cd /Users/<USER>/Desktop/ltst/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build /Users/<USER>/Desktop/ltst/build/CMakeFiles/MacOS3DLooterShooter.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/depend

