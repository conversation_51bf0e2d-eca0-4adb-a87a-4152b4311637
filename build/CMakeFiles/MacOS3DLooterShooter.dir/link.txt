/usr/bin/c++  -arch arm64 -mmacosx-version-min=10.15 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/MacOS3DLooterShooter.dir/src/main.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/engine/Engine.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/graphics/SimpleRenderSystem.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/AssetManager.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/ModelAsset.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/assets/TextureAsset.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/input/CocoaInputHandler.mm.o CMakeFiles/MacOS3DLooterShooter.dir/src/input/InputSystem.cpp.o CMakeFiles/MacOS3DLooterShooter.dir/src/camera/CameraSystem.cpp.o -o MacOS3DLooterShooter  -framework Metal -framework MetalKit -framework Foundation -framework Cocoa -framework QuartzCore -framework OpenGL
