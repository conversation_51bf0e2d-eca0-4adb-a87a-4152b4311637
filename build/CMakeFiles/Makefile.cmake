# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeOBJCXXInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/AppleClang-OBJCXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/Clang-OBJCXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/Clang.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Compiler/GNU.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/FindDoxygen.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/FindPackageMessage.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Internal/CMakeOBJCXXLinkerInformation.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-OBJCXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Apple-Clang-OBJCXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Darwin.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/Users/<USER>/.local/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/Users/<USER>/Desktop/ltst/CMakeLists.txt"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeOBJCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "/Users/<USER>/Desktop/ltst/tests/CMakeLists.txt"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "tests/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/MacOS3DLooterShooter.dir/DependInfo.cmake"
  "CMakeFiles/copy_assets.dir/DependInfo.cmake"
  "tests/CMakeFiles/game_tests.dir/DependInfo.cmake"
  )
