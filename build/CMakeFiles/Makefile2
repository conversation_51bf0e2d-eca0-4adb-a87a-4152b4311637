# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.local/homebrew/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.local/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Desktop/ltst

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Desktop/ltst/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/MacOS3DLooterShooter.dir/all
all: CMakeFiles/compile_shaders.dir/all
all: CMakeFiles/copy_assets.dir/all
all: tests/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/MacOS3DLooterShooter.dir/codegen
codegen: CMakeFiles/compile_shaders.dir/codegen
codegen: CMakeFiles/copy_assets.dir/codegen
codegen: tests/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: tests/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/MacOS3DLooterShooter.dir/clean
clean: CMakeFiles/compile_shaders.dir/clean
clean: CMakeFiles/copy_assets.dir/clean
clean: tests/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all: tests/CMakeFiles/game_tests.dir/all
.PHONY : tests/all

# Recursive "codegen" directory target.
tests/codegen: tests/CMakeFiles/game_tests.dir/codegen
.PHONY : tests/codegen

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/game_tests.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/MacOS3DLooterShooter.dir

# All Build rule for target.
CMakeFiles/MacOS3DLooterShooter.dir/all: CMakeFiles/copy_assets.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14 "Built target MacOS3DLooterShooter"
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MacOS3DLooterShooter.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/MacOS3DLooterShooter.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/rule

# Convenience name for target.
MacOS3DLooterShooter: CMakeFiles/MacOS3DLooterShooter.dir/rule
.PHONY : MacOS3DLooterShooter

# codegen rule for target.
CMakeFiles/MacOS3DLooterShooter.dir/codegen: CMakeFiles/copy_assets.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14 "Finished codegen for target MacOS3DLooterShooter"
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/codegen

# clean rule for target.
CMakeFiles/MacOS3DLooterShooter.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MacOS3DLooterShooter.dir/build.make CMakeFiles/MacOS3DLooterShooter.dir/clean
.PHONY : CMakeFiles/MacOS3DLooterShooter.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/compile_shaders.dir

# All Build rule for target.
CMakeFiles/compile_shaders.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/compile_shaders.dir/build.make CMakeFiles/compile_shaders.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/compile_shaders.dir/build.make CMakeFiles/compile_shaders.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=15 "Built target compile_shaders"
.PHONY : CMakeFiles/compile_shaders.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/compile_shaders.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/compile_shaders.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : CMakeFiles/compile_shaders.dir/rule

# Convenience name for target.
compile_shaders: CMakeFiles/compile_shaders.dir/rule
.PHONY : compile_shaders

# codegen rule for target.
CMakeFiles/compile_shaders.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/compile_shaders.dir/build.make CMakeFiles/compile_shaders.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=15 "Finished codegen for target compile_shaders"
.PHONY : CMakeFiles/compile_shaders.dir/codegen

# clean rule for target.
CMakeFiles/compile_shaders.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/compile_shaders.dir/build.make CMakeFiles/compile_shaders.dir/clean
.PHONY : CMakeFiles/compile_shaders.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/copy_assets.dir

# All Build rule for target.
CMakeFiles/copy_assets.dir/all: CMakeFiles/compile_shaders.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_assets.dir/build.make CMakeFiles/copy_assets.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_assets.dir/build.make CMakeFiles/copy_assets.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=16 "Built target copy_assets"
.PHONY : CMakeFiles/copy_assets.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/copy_assets.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/copy_assets.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : CMakeFiles/copy_assets.dir/rule

# Convenience name for target.
copy_assets: CMakeFiles/copy_assets.dir/rule
.PHONY : copy_assets

# codegen rule for target.
CMakeFiles/copy_assets.dir/codegen: CMakeFiles/compile_shaders.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_assets.dir/build.make CMakeFiles/copy_assets.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=16 "Finished codegen for target copy_assets"
.PHONY : CMakeFiles/copy_assets.dir/codegen

# clean rule for target.
CMakeFiles/copy_assets.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/copy_assets.dir/build.make CMakeFiles/copy_assets.dir/clean
.PHONY : CMakeFiles/copy_assets.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/game_tests.dir

# All Build rule for target.
tests/CMakeFiles/game_tests.dir/all:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=17,18,19,20,21,22,23 "Built target game_tests"
.PHONY : tests/CMakeFiles/game_tests.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/game_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/game_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Desktop/ltst/build/CMakeFiles 0
.PHONY : tests/CMakeFiles/game_tests.dir/rule

# Convenience name for target.
game_tests: tests/CMakeFiles/game_tests.dir/rule
.PHONY : game_tests

# codegen rule for target.
tests/CMakeFiles/game_tests.dir/codegen:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Desktop/ltst/build/CMakeFiles --progress-num=17,18,19,20,21,22,23 "Finished codegen for target game_tests"
.PHONY : tests/CMakeFiles/game_tests.dir/codegen

# clean rule for target.
tests/CMakeFiles/game_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/game_tests.dir/build.make tests/CMakeFiles/game_tests.dir/clean
.PHONY : tests/CMakeFiles/game_tests.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

