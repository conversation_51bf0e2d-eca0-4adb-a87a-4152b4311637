cmake_minimum_required(VERSION 3.20)
project(MacOS3DLooterShooter VERSION 1.0.0 LANGUAGES CXX OBJCXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Platform check
if(NOT APPLE)
    message(FATAL_ERROR "This project is designed specifically for macOS")
endif()

# Build configuration
set(CMAKE_BUILD_TYPE Debug CACHE STRING "Build type")
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "RelWithDebInfo")

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELWITHDEBINFO "-O2 -g -DNDEBUG")

# macOS specific settings
set(CMAKE_OSX_DEPLOYMENT_TARGET "10.15")
set(<PERSON><PERSON><PERSON>_XCODE_ATTRIBUTE_CLANG_ENABLE_OBJC_ARC YES)

# Find required frameworks
find_library(METAL_FRAMEWORK Metal REQUIRED)
find_library(METALKIT_FRAMEWORK MetalKit REQUIRED)
find_library(FOUNDATION_FRAMEWORK Foundation REQUIRED)
find_library(COCOA_FRAMEWORK Cocoa REQUIRED)
find_library(QUARTZCORE_FRAMEWORK QuartzCore REQUIRED)
find_library(OPENGL_FRAMEWORK OpenGL)

# Include directories
include_directories(src)
include_directories(src/engine)
include_directories(src/graphics)
include_directories(src/assets)
include_directories(src/input)
include_directories(src/camera)
include_directories(src/physics)
include_directories(src/ai)
include_directories(src/utils)

# Source files
file(GLOB_RECURSE ENGINE_SOURCES "src/engine/*.cpp" "src/engine/*.mm")
file(GLOB_RECURSE GAME_SOURCES "src/game/*.cpp" "src/game/*.mm")
# Graphics sources
file(GLOB GRAPHICS_SOURCES
    "src/graphics/SimpleRenderSystem.cpp"
    "src/graphics/WindowManager.mm"
    "src/graphics/MetalDevice.mm"
)
file(GLOB_RECURSE ASSETS_SOURCES "src/assets/*.cpp" "src/assets/*.mm")
file(GLOB_RECURSE INPUT_SOURCES "src/input/*.cpp" "src/input/*.mm")
file(GLOB_RECURSE CAMERA_SOURCES "src/camera/*.cpp" "src/camera/*.mm")
file(GLOB_RECURSE PHYSICS_SOURCES "src/physics/*.cpp" "src/physics/*.mm")
file(GLOB_RECURSE AI_SOURCES "src/ai/*.cpp" "src/ai/*.mm")
file(GLOB_RECURSE UTILS_SOURCES "src/utils/*.cpp" "src/utils/*.mm")
# file(GLOB_RECURSE GUI_SOURCES "src/gui/*.cpp" "src/gui/*.mm") # Temporarily disabled

# Main source files
set(MAIN_SOURCES src/main.mm)

# Combine all sources
set(ALL_SOURCES
    ${MAIN_SOURCES}
    ${ENGINE_SOURCES}
    ${GAME_SOURCES}
    ${GRAPHICS_SOURCES}
    ${ASSETS_SOURCES}
    ${INPUT_SOURCES}
    ${CAMERA_SOURCES}
    ${PHYSICS_SOURCES}
    ${AI_SOURCES}
    ${UTILS_SOURCES}
    # ${GUI_SOURCES} # Temporarily disabled
)

# Main executable
add_executable(${PROJECT_NAME} ${ALL_SOURCES})

# Link frameworks
target_link_libraries(${PROJECT_NAME}
    ${METAL_FRAMEWORK}
    ${METALKIT_FRAMEWORK}
    ${FOUNDATION_FRAMEWORK}
    ${COCOA_FRAMEWORK}
    ${QUARTZCORE_FRAMEWORK}
)

# Optional OpenGL fallback
if(OPENGL_FRAMEWORK)
    target_link_libraries(${PROJECT_NAME} ${OPENGL_FRAMEWORK})
    target_compile_definitions(${PROJECT_NAME} PRIVATE OPENGL_FALLBACK_AVAILABLE)
endif()

# Asset copying
add_custom_target(copy_assets ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/assets
    ${CMAKE_BINARY_DIR}/assets
    COMMENT "Copying assets to build directory"
)

add_dependencies(${PROJECT_NAME} copy_assets)

# Install target
install(TARGETS ${PROJECT_NAME} DESTINATION bin)
install(DIRECTORY assets/ DESTINATION bin/assets)

# Testing
enable_testing()
add_subdirectory(tests)

# Documentation
find_package(Doxygen)
if(DOXYGEN_FOUND)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in 
                   ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
    add_custom_target(docs
        ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen" VERBATIM
    )
endif()
