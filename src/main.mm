//
// main.cpp
// macOS 3D Looter-Shooter Game
//
// Main entry point for the game application
//

#include <iostream>
#include <memory>
#include <chrono>

// Engine includes
#include "engine/Engine.h"
#include "engine/Components.h"
#include "engine/TestSystems.h"
#include "graphics/SimpleRenderSystem.h"
#include "graphics/WindowManager.h"
#include "graphics/MetalDevice.h"

// Asset system includes
#include "assets/AssetManager.h"
#include "assets/TextureAsset.h"
#include "assets/ModelAsset.h"

// GUI system includes (temporarily disabled)
// #include "gui/GUI.h"
#include "input/InputSystem.h"

int main(int argc, char* argv[]) {
    std::cout << "🎮 macOS 3D Looter-Shooter Game" << std::endl;
    std::cout << "================================" << std::endl;
    
    try {
        // Parse command line arguments
        bool debugMode = false;
        bool fullscreen = false;
        bool enableGUI = false; // Temporarily disabled to fix window system first
        bool showGUIDemo = false;

        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--debug" || arg == "-d") {
                debugMode = true;
            } else if (arg == "--fullscreen" || arg == "-f") {
                fullscreen = true;
            } else if (arg == "--no-gui") {
                enableGUI = false;
            } else if (arg == "--gui-demo") {
                showGUIDemo = true;
            } else if (arg == "--help" || arg == "-h") {
                std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
                std::cout << "Options:" << std::endl;
                std::cout << "  --debug, -d      Enable debug mode" << std::endl;
                std::cout << "  --fullscreen, -f Enable fullscreen mode" << std::endl;
                std::cout << "  --no-gui         Disable GUI system" << std::endl;
                std::cout << "  --gui-demo       Show GUI demo on startup" << std::endl;
                std::cout << "  --help, -h       Show this help message" << std::endl;
                return 0;
            }
        }
        
        if (debugMode) {
            std::cout << "🐛 Debug mode enabled" << std::endl;
        }
        
        if (fullscreen) {
            std::cout << "🖥️  Fullscreen mode enabled" << std::endl;
        }

        if (enableGUI) {
            std::cout << "🎨 GUI system enabled" << std::endl;
        }

        if (showGUIDemo) {
            std::cout << "🎮 GUI demo mode enabled" << std::endl;
        }
        
        // Initialize engine configuration
        Engine::EngineConfig config;
        config.enableDebugMode = debugMode;
        config.fullscreen = fullscreen;
        config.targetFPS = 60.0f;

        // Create and initialize engine
        Engine::Engine engine;
        if (!engine.Initialize(config)) {
            std::cerr << "❌ Failed to initialize engine" << std::endl;
            return 1;
        }

        // Initialize Metal device
        std::cout << "🔧 Initializing Metal device..." << std::endl;
        Engine::Graphics::MetalDevice metalDevice;
        if (!metalDevice.Initialize()) {
            std::cerr << "❌ Failed to initialize Metal device" << std::endl;
            return 1;
        }

        // Initialize window system
        std::cout << "🪟 Initializing window system..." << std::endl;
        Engine::Graphics::WindowManager windowManager;
        Engine::Graphics::WindowConfig windowConfig;
        windowConfig.width = config.windowWidth;
        windowConfig.height = config.windowHeight;
        windowConfig.fullscreen = config.fullscreen;
        windowConfig.title = config.gameName + " v" + config.gameVersion;

        if (!windowManager.Initialize(windowConfig)) {
            std::cerr << "❌ Failed to initialize window system" << std::endl;
            return 1;
        }

        // Show the window
        windowManager.Show();

        // Initialize Asset Management System
        std::cout << "📁 Initializing Asset Management System..." << std::endl;
        auto& assetManager = Engine::Assets::AssetManager::GetInstance();
        if (!assetManager.Initialize("assets/")) {
            std::cerr << "❌ Failed to initialize Asset Manager" << std::endl;
            return 1;
        }

        // Initialize asset caches
        Engine::Assets::TextureCache::GetInstance().Initialize(nullptr); // TODO: Pass Metal device
        Engine::Assets::ModelCache::GetInstance().Initialize(nullptr);   // TODO: Pass Metal device

        std::cout << "✅ Asset Management System initialized" << std::endl;

        // Initialize Input System
        std::cout << "🎮 Initializing Input System..." << std::endl;
        Engine::Input::InputManager& inputManager = Engine::Input::InputManager::GetInstance();
        inputManager.Initialize();
        auto* inputSystem = inputManager.GetInputSystem();
        std::cout << "✅ Input System initialized" << std::endl;

        // Initialize GUI System (temporarily disabled)
        /*
        if (enableGUI) {
            std::cout << "🎨 Initializing GUI System..." << std::endl;

            // Note: In a real implementation, you would get these from the graphics system
            // For now, we'll pass nullptr and handle it gracefully in the GUI system
            if (Engine::GUI::InitializeGUISystem(&engine.GetECS(), inputSystem,
                                                nullptr, nullptr, nullptr)) {
                std::cout << "✅ GUI System initialized" << std::endl;

                // Set up GUI configuration
                Engine::GUI::UIConfig guiConfig;
                guiConfig.enableDebugRendering = debugMode;
                guiConfig.enableInputDebug = debugMode;
                guiConfig.enableLayoutDebug = debugMode;
                Engine::GUI::SetGUIConfig(guiConfig);

                // Create demo content if requested
                if (showGUIDemo) {
                    Engine::GUI::CreateGameMenus();
                    Engine::GUI::CreateGameHUD();
                    Engine::GUI::ShowMainMenu();

                    // Set up demo event handler
                    Engine::GUI::RegisterGUIEventHandler([](const Engine::GUI::UIInputEvent& event) {
                        std::cout << "🎯 GUI Event: " << static_cast<int>(event.type) << std::endl;
                    });
                }
            } else {
                std::cerr << "❌ Failed to initialize GUI System" << std::endl;
                enableGUI = false;
            }
        }
        */

        // Register components
        auto& ecs = engine.GetECS();
        ecs.RegisterComponent<Engine::Transform>();
        ecs.RegisterComponent<Engine::Name>();
        ecs.RegisterComponent<Engine::Health>();
        ecs.RegisterComponent<Engine::Velocity>();
        ecs.RegisterComponent<Engine::Renderable>();

        // Register graphics components
        Engine::Graphics::RegisterSimpleGraphicsComponents(ecs);

        // Register and configure systems
        auto movementSystem = ecs.RegisterSystem<Engine::MovementSystem>();
        auto healthSystem = ecs.RegisterSystem<Engine::HealthSystem>();
        auto debugSystem = ecs.RegisterSystem<Engine::DebugSystem>();
        auto renderSystem = ecs.RegisterSystem<Engine::Graphics::SimpleRenderSystem>();

        // Set system signatures (which components they require)
        Engine::ComponentSignature movementSignature;
        movementSignature.set(ecs.GetComponentType<Engine::Transform>());
        movementSignature.set(ecs.GetComponentType<Engine::Velocity>());
        ecs.SetSystemSignature<Engine::MovementSystem>(movementSignature);

        Engine::ComponentSignature healthSignature;
        healthSignature.set(ecs.GetComponentType<Engine::Health>());
        ecs.SetSystemSignature<Engine::HealthSystem>(healthSignature);

        Engine::ComponentSignature debugSignature;
        debugSignature.set(ecs.GetComponentType<Engine::Transform>());
        ecs.SetSystemSignature<Engine::DebugSystem>(debugSignature);

        Engine::ComponentSignature renderSignature;
        renderSignature.set(ecs.GetComponentType<Engine::Transform>());
        renderSignature.set(ecs.GetComponentType<Engine::Graphics::SimpleMesh>());
        ecs.SetSystemSignature<Engine::Graphics::SimpleRenderSystem>(renderSignature);

        // Create a camera
        auto camera = Engine::Graphics::CreateSimpleCameraEntity(ecs, {0.0f, 0.0f, 5.0f}, {0.0f, 0.0f, 0.0f});
        renderSystem->SetActiveCamera(camera);

        // Create some test entities
        std::cout << "🎯 Creating test entities..." << std::endl;

        // Create a moving player entity with graphics
        auto player = ecs.CreateEntity();
        ecs.AddComponent(player, Engine::Name("Player"));
        ecs.AddComponent(player, Engine::Transform({0.0f, 0.0f, 0.0f}));
        ecs.AddComponent(player, Engine::Health(100.0f));
        ecs.AddComponent(player, Engine::Velocity({1.0f, 0.0f, 0.0f})); // Moving right
        ecs.AddComponent(player, Engine::Graphics::SimpleMesh("player_cube", {0.0f, 1.0f, 0.0f, 1.0f})); // Green

        // Create a stationary enemy entity with graphics
        auto enemy = ecs.CreateEntity();
        ecs.AddComponent(enemy, Engine::Name("Enemy"));
        ecs.AddComponent(enemy, Engine::Transform({10.0f, 0.0f, 0.0f}));
        ecs.AddComponent(enemy, Engine::Health(50.0f));
        ecs.AddComponent(enemy, Engine::Velocity({0.0f, 0.0f, 0.0f})); // Stationary
        ecs.AddComponent(enemy, Engine::Graphics::SimpleMesh("enemy_cube", {1.0f, 0.0f, 0.0f, 1.0f})); // Red

        // Create a moving projectile entity with graphics
        auto projectile = ecs.CreateEntity();
        ecs.AddComponent(projectile, Engine::Name("Projectile"));
        ecs.AddComponent(projectile, Engine::Transform({-5.0f, 0.0f, 0.0f}));
        ecs.AddComponent(projectile, Engine::Velocity({5.0f, 0.0f, 0.0f})); // Fast moving
        ecs.AddComponent(projectile, Engine::Graphics::SimpleMesh("projectile_sphere", {1.0f, 1.0f, 0.0f, 1.0f})); // Yellow

        std::cout << "✅ Created " << ecs.GetEntityCount() << " test entities" << std::endl;

        // Demonstrate Asset Management System
        std::cout << "📁 Demonstrating Asset Management System..." << std::endl;

        // Load some test assets
        auto cubeModel = assetManager.LoadAsset<Engine::Assets::ModelAsset>("models/cube.obj");
        auto playerTexture = assetManager.LoadAsset<Engine::Assets::TextureAsset>("textures/player.png");
        auto enemyTexture = assetManager.LoadAsset<Engine::Assets::TextureAsset>("textures/enemy.png");

        // Get primitive models from cache
        auto& modelCache = Engine::Assets::ModelCache::GetInstance();
        auto primitiveModel = modelCache.GetCube();

        // Get default textures from cache
        auto& textureCache = Engine::Assets::TextureCache::GetInstance();
        auto whiteTexture = textureCache.GetWhiteTexture();

        // Print asset statistics
        auto assetStats = assetManager.GetStatistics();
        std::cout << "📊 Asset Statistics:" << std::endl;
        std::cout << "   Total Assets: " << assetStats.totalAssets << std::endl;
        std::cout << "   Loaded Assets: " << assetStats.loadedAssets << std::endl;
        std::cout << "   Memory Usage: " << (assetStats.memoryUsage / 1024) << " KB" << std::endl;
        std::cout << "   Cache Hits: " << assetStats.cacheHits << std::endl;
        std::cout << "   Cache Misses: " << assetStats.cacheMisses << std::endl;

        // Run the engine for a few seconds to demonstrate the ECS and GUI
        float demoTime = enableGUI && showGUIDemo ? 30.0f : 10.0f; // Longer demo if GUI is enabled
        std::cout << "🎮 Running engine demo for " << demoTime << " seconds..." << std::endl;

        // Set a timer to stop the demo
        auto startTime = std::chrono::high_resolution_clock::now();
        auto lastFrameTime = startTime;

        while (engine.IsRunning() && !windowManager.ShouldClose()) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration<float>(currentTime - startTime).count();
            float deltaTime = std::chrono::duration<float>(currentTime - lastFrameTime).count();
            lastFrameTime = currentTime;

            // Poll window events
            windowManager.PollEvents();

            if (elapsed >= demoTime) {
                std::cout << "⏰ Demo time completed" << std::endl;
                engine.RequestShutdown();
                break;
            }

            // Update GUI system (temporarily disabled)
            /*
            if (enableGUI) {
                Engine::GUI::UpdateGUISystem(deltaTime);

                // Handle GUI demo switching
                if (showGUIDemo) {
                    static float demoSwitchTimer = 0.0f;
                    demoSwitchTimer += deltaTime;

                    if (demoSwitchTimer > 5.0f) {  // Switch demo every 5 seconds
                        auto* guiDemo = Engine::GUI::GetGUIDemo();
                        if (guiDemo) {
                            auto availableDemos = guiDemo->GetAvailableDemos();
                            static int currentDemoIndex = 0;

                            if (!availableDemos.empty()) {
                                currentDemoIndex = (currentDemoIndex + 1) % availableDemos.size();
                                guiDemo->ShowDemo(availableDemos[currentDemoIndex]);
                                std::cout << "🎯 Switched to demo: " << availableDemos[currentDemoIndex] << std::endl;
                            }
                        }
                        demoSwitchTimer = 0.0f;
                    }
                }
            }
            */

            // Simulate some damage to test the health system
            if (static_cast<int>(elapsed) == 5 && static_cast<int>(elapsed * 10) % 10 == 0) {
                auto& enemyHealth = ecs.GetComponent<Engine::Health>(enemy);
                enemyHealth.TakeDamage(60.0f); // Kill the enemy
                std::cout << "💥 Enemy took fatal damage!" << std::endl;
            }

            // Let the engine run one frame
            engine.Run();
        }

        std::cout << "🏁 Engine demo completed successfully!" << std::endl;

        // Print final render statistics
        const auto& renderStats = renderSystem->GetStats();
        std::cout << "📊 Final Render Statistics:" << std::endl;
        std::cout << "   Total Frames: " << renderStats.frameCount << std::endl;
        std::cout << "   Total Draw Calls: " << renderStats.drawCalls << std::endl;
        std::cout << "   Total Entities Rendered: " << renderStats.entities << std::endl;

        // Shutdown GUI System (temporarily disabled)
        /*
        if (enableGUI) {
            std::cout << "🎨 Shutting down GUI System..." << std::endl;
            Engine::GUI::ShutdownGUISystem();
            std::cout << "✅ GUI System shutdown complete" << std::endl;
        }
        */

        // Shutdown Input System
        std::cout << "🎮 Shutting down Input System..." << std::endl;
        Engine::Input::InputManager::GetInstance().Shutdown();
        std::cout << "✅ Input System shutdown complete" << std::endl;

        // Shutdown Asset Management System
        std::cout << "📁 Shutting down Asset Management System..." << std::endl;
        Engine::Assets::ModelCache::GetInstance().Shutdown();
        Engine::Assets::TextureCache::GetInstance().Shutdown();
        assetManager.Shutdown();
        std::cout << "✅ Asset Management System shutdown complete" << std::endl;

        // Shutdown window system
        std::cout << "🪟 Shutting down window system..." << std::endl;
        windowManager.Shutdown();
        std::cout << "✅ Window system shutdown complete" << std::endl;

        // Shutdown Metal device
        std::cout << "🔧 Shutting down Metal device..." << std::endl;
        metalDevice.Shutdown();
        std::cout << "✅ Metal device shutdown complete" << std::endl;

        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
