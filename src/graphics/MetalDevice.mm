//
// MetalDevice.mm
// macOS 3D Looter-Shooter Game Engine
//
// Metal device and context implementation for macOS
//

#include "MetalDevice.h"
#include <iostream>
#include <Foundation/Foundation.h>

namespace Engine {
namespace Graphics {

// Static member definitions
MetalDevice* MetalDevice::s_instance = nullptr;

// ============================================================================
// MetalDevice Implementation
// ============================================================================

MetalDevice::MetalDevice() 
    : m_device(nil)
    , m_commandQueue(nil)
    , m_defaultLibrary(nil)
    , m_isLowPower(false)
    , m_initialized(false) {
    
    ENGINE_ASSERT(s_instance == nullptr, "MetalDevice instance already exists");
    s_instance = this;
}

MetalDevice::~MetalDevice() {
    if (m_initialized) {
        Shutdown();
    }
    s_instance = nullptr;
}

bool MetalDevice::Initialize() {
    ENGINE_ASSERT(!m_initialized, "MetalDevice already initialized");
    
    std::cout << "🔧 Initializing Metal device..." << std::endl;
    
    if (!CreateDevice()) {
        std::cerr << "❌ Failed to create Metal device" << std::endl;
        return false;
    }
    
    if (!CreateCommandQueue()) {
        std::cerr << "❌ Failed to create Metal command queue" << std::endl;
        return false;
    }
    
    if (!CreateDefaultLibrary()) {
        std::cerr << "❌ Failed to create Metal default library" << std::endl;
        return false;
    }
    
    QueryDeviceCapabilities();
    
    m_initialized = true;
    std::cout << "✅ Metal device initialized successfully" << std::endl;
    PrintDeviceInfo();
    
    return true;
}

void MetalDevice::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🔧 Shutting down Metal device..." << std::endl;
    
    if (m_defaultLibrary) {
        [m_defaultLibrary release];
        m_defaultLibrary = nil;
    }
    
    if (m_commandQueue) {
        [m_commandQueue release];
        m_commandQueue = nil;
    }
    
    if (m_device) {
        [m_device release];
        m_device = nil;
    }
    
    m_initialized = false;
    std::cout << "✅ Metal device shutdown complete" << std::endl;
}

bool MetalDevice::CreateDevice() {
    // Try to get the default Metal device
    m_device = MTLCreateSystemDefaultDevice();
    if (!m_device) {
        std::cerr << "❌ No Metal-capable device found" << std::endl;
        return false;
    }
    
    [m_device retain];
    
    // Get device name
    NSString* deviceName = [m_device name];
    m_deviceName = std::string([deviceName UTF8String]);
    
    // Check if it's a low-power device
    m_isLowPower = [m_device isLowPower];
    
    return true;
}

bool MetalDevice::CreateCommandQueue() {
    m_commandQueue = [m_device newCommandQueue];
    if (!m_commandQueue) {
        std::cerr << "❌ Failed to create Metal command queue" << std::endl;
        return false;
    }
    
    // Set debug label for debugging
    [m_commandQueue setLabel:@"Main Command Queue"];
    
    return true;
}

bool MetalDevice::CreateDefaultLibrary() {
    m_defaultLibrary = [m_device newDefaultLibrary];
    if (!m_defaultLibrary) {
        std::cerr << "❌ Failed to create Metal default library" << std::endl;
        return false;
    }
    
    return true;
}

void MetalDevice::QueryDeviceCapabilities() {
    std::cout << "🔍 Querying Metal device capabilities..." << std::endl;
    
    // Check feature sets
    if ([m_device supportsFeatureSet:MTLFeatureSet_macOS_GPUFamily2_v1]) {
        std::cout << "  ✅ Supports macOS GPU Family 2 v1" << std::endl;
    }
    
    // Check GPU families (macOS 10.15+)
    if (@available(macOS 10.15, *)) {
        if ([m_device supportsFamily:MTLGPUFamilyMac2]) {
            std::cout << "  ✅ Supports Mac GPU Family 2" << std::endl;
        }
        if ([m_device supportsFamily:MTLGPUFamilyApple7]) {
            std::cout << "  ✅ Supports Apple GPU Family 7 (Apple Silicon)" << std::endl;
        }
    }
    
    // Print memory info
    std::cout << "  📊 Max buffer length: " << GetMaxBufferLength() / (1024 * 1024) << " MB" << std::endl;
    std::cout << "  📊 Recommended max working set: " << GetRecommendedMaxWorkingSetSize() / (1024 * 1024) << " MB" << std::endl;
}

bool MetalDevice::SupportsFeatureSet(MTLFeatureSet featureSet) const {
    return [m_device supportsFeatureSet:featureSet];
}

bool MetalDevice::SupportsFamily(MTLGPUFamily family) const {
    if (@available(macOS 10.15, *)) {
        return [m_device supportsFamily:family];
    }
    return false;
}

size_t MetalDevice::GetMaxBufferLength() const {
    return [m_device maxBufferLength];
}

size_t MetalDevice::GetRecommendedMaxWorkingSetSize() const {
    return [m_device recommendedMaxWorkingSetSize];
}

void MetalDevice::PrintDeviceInfo() const {
    std::cout << "=== Metal Device Information ===" << std::endl;
    std::cout << "Device Name: " << m_deviceName << std::endl;
    std::cout << "Low Power: " << (m_isLowPower ? "Yes" : "No") << std::endl;
    std::cout << "Max Buffer Length: " << GetMaxBufferLength() / (1024 * 1024) << " MB" << std::endl;
    std::cout << "Recommended Max Working Set: " << GetRecommendedMaxWorkingSetSize() / (1024 * 1024) << " MB" << std::endl;
    std::cout << "===============================" << std::endl;
}

// ============================================================================
// MetalContext Implementation
// ============================================================================

MetalContext::MetalContext()
    : m_device(nullptr)
    , m_colorTexture(nil)
    , m_depthTexture(nil)
    , m_renderPassDescriptor(nil)
    , m_currentCommandBuffer(nil)
    , m_currentRenderEncoder(nil)
    , m_width(0)
    , m_height(0)
    , m_vsyncEnabled(true)
    , m_initialized(false)
    , m_frameCount(0) {
}

MetalContext::~MetalContext() {
    if (m_initialized) {
        Shutdown();
    }
}

bool MetalContext::Initialize(int width, int height, bool vsync) {
    ENGINE_ASSERT(!m_initialized, "MetalContext already initialized");
    
    m_device = MetalDevice::GetInstance();
    ENGINE_ASSERT(m_device && m_device->GetDevice(), "MetalDevice must be initialized first");
    
    m_width = width;
    m_height = height;
    m_vsyncEnabled = vsync;
    
    std::cout << "🔧 Initializing Metal context (" << width << "x" << height << ")..." << std::endl;
    
    if (!CreateRenderTargets()) {
        std::cerr << "❌ Failed to create render targets" << std::endl;
        return false;
    }
    
    if (!CreateRenderPassDescriptor()) {
        std::cerr << "❌ Failed to create render pass descriptor" << std::endl;
        return false;
    }
    
    m_initialized = true;
    std::cout << "✅ Metal context initialized successfully" << std::endl;
    
    return true;
}

void MetalContext::Shutdown() {
    if (!m_initialized) {
        return;
    }
    
    std::cout << "🔧 Shutting down Metal context..." << std::endl;
    
    if (m_renderPassDescriptor) {
        [m_renderPassDescriptor release];
        m_renderPassDescriptor = nil;
    }
    
    if (m_depthTexture) {
        [m_depthTexture release];
        m_depthTexture = nil;
    }
    
    if (m_colorTexture) {
        [m_colorTexture release];
        m_colorTexture = nil;
    }
    
    m_initialized = false;
    std::cout << "✅ Metal context shutdown complete" << std::endl;
}

bool MetalContext::CreateRenderTargets() {
    id<MTLDevice> device = m_device->GetDevice();
    
    // Create color texture
    MTLTextureDescriptor* colorDesc = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatBGRA8Unorm
                                                                                         width:m_width
                                                                                        height:m_height
                                                                                     mipmapped:NO];
    colorDesc.usage = MTLTextureUsageRenderTarget | MTLTextureUsageShaderRead;
    colorDesc.storageMode = MTLStorageModePrivate;
    
    m_colorTexture = [device newTextureWithDescriptor:colorDesc];
    if (!m_colorTexture) {
        std::cerr << "❌ Failed to create color texture" << std::endl;
        return false;
    }
    [m_colorTexture setLabel:@"Color Render Target"];
    
    // Create depth texture
    MTLTextureDescriptor* depthDesc = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:MTLPixelFormatDepth32Float
                                                                                         width:m_width
                                                                                        height:m_height
                                                                                     mipmapped:NO];
    depthDesc.usage = MTLTextureUsageRenderTarget;
    depthDesc.storageMode = MTLStorageModePrivate;
    
    m_depthTexture = [device newTextureWithDescriptor:depthDesc];
    if (!m_depthTexture) {
        std::cerr << "❌ Failed to create depth texture" << std::endl;
        return false;
    }
    [m_depthTexture setLabel:@"Depth Render Target"];
    
    return true;
}

bool MetalContext::CreateRenderPassDescriptor() {
    m_renderPassDescriptor = [[MTLRenderPassDescriptor alloc] init];
    
    // Configure color attachment
    m_renderPassDescriptor.colorAttachments[0].texture = m_colorTexture;
    m_renderPassDescriptor.colorAttachments[0].loadAction = MTLLoadActionClear;
    m_renderPassDescriptor.colorAttachments[0].storeAction = MTLStoreActionStore;
    m_renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColorMake(0.2, 0.3, 0.4, 1.0); // Dark blue-gray
    
    // Configure depth attachment
    m_renderPassDescriptor.depthAttachment.texture = m_depthTexture;
    m_renderPassDescriptor.depthAttachment.loadAction = MTLLoadActionClear;
    m_renderPassDescriptor.depthAttachment.storeAction = MTLStoreActionDontCare;
    m_renderPassDescriptor.depthAttachment.clearDepth = 1.0;
    
    return true;
}

void MetalContext::BeginFrame() {
    ENGINE_ASSERT(m_initialized, "MetalContext not initialized");
    
    m_currentCommandBuffer = [m_device->GetCommandQueue() commandBuffer];
    [m_currentCommandBuffer setLabel:@"Frame Command Buffer"];
    
    ++m_frameCount;
}

void MetalContext::EndFrame() {
    ENGINE_ASSERT(m_currentCommandBuffer, "No active command buffer");
    
    if (m_currentRenderEncoder) {
        [m_currentRenderEncoder endEncoding];
        m_currentRenderEncoder = nil;
    }
}

void MetalContext::Present() {
    ENGINE_ASSERT(m_currentCommandBuffer, "No active command buffer");
    
    [m_currentCommandBuffer commit];
    [m_currentCommandBuffer waitUntilCompleted];
    
    m_currentCommandBuffer = nil;
}

id<MTLCommandBuffer> MetalContext::CreateCommandBuffer() {
    return [m_device->GetCommandQueue() commandBuffer];
}

id<MTLRenderCommandEncoder> MetalContext::CreateRenderEncoder(MTLRenderPassDescriptor* renderPass) {
    ENGINE_ASSERT(m_currentCommandBuffer, "No active command buffer");
    
    if (!renderPass) {
        renderPass = m_renderPassDescriptor;
    }
    
    return [m_currentCommandBuffer renderCommandEncoderWithDescriptor:renderPass];
}

MTLRenderPassDescriptor* MetalContext::GetCurrentRenderPassDescriptor() {
    return m_renderPassDescriptor;
}

id<MTLTexture> MetalContext::GetCurrentDrawable() {
    return m_colorTexture;
}

// ============================================================================
// MetalUtils Implementation
// ============================================================================

namespace MetalUtils {

bool CheckError(NSError* error, const char* operation) {
    if (error) {
        NSString* description = [error localizedDescription];
        std::cerr << "❌ Metal error in " << operation << ": " << [description UTF8String] << std::endl;
        return false;
    }
    return true;
}

MTLPixelFormat GetOptimalColorFormat() {
    return MTLPixelFormatBGRA8Unorm; // Standard format for macOS
}

MTLPixelFormat GetOptimalDepthFormat() {
    return MTLPixelFormatDepth32Float; // High precision depth
}

id<MTLBuffer> CreateBuffer(id<MTLDevice> device, const void* data, size_t size, MTLResourceOptions options) {
    id<MTLBuffer> buffer = [device newBufferWithBytes:data length:size options:options];
    if (!buffer) {
        std::cerr << "❌ Failed to create Metal buffer of size " << size << " bytes" << std::endl;
        return nil;
    }
    return buffer;
}

id<MTLTexture> CreateTexture2D(id<MTLDevice> device, int width, int height, MTLPixelFormat format, MTLTextureUsage usage) {
    MTLTextureDescriptor* descriptor = [MTLTextureDescriptor texture2DDescriptorWithPixelFormat:format
                                                                                          width:width
                                                                                         height:height
                                                                                      mipmapped:NO];
    descriptor.usage = usage;
    descriptor.storageMode = MTLStorageModePrivate;

    id<MTLTexture> texture = [device newTextureWithDescriptor:descriptor];
    if (!texture) {
        std::cerr << "❌ Failed to create Metal texture (" << width << "x" << height << ")" << std::endl;
        return nil;
    }

    return texture;
}

id<MTLFunction> LoadFunction(id<MTLLibrary> library, const char* functionName) {
    NSString* name = [NSString stringWithUTF8String:functionName];
    id<MTLFunction> function = [library newFunctionWithName:name];
    if (!function) {
        std::cerr << "❌ Failed to load Metal function: " << functionName << std::endl;
        return nil;
    }
    return function;
}

id<MTLRenderPipelineState> CreateRenderPipelineState(id<MTLDevice> device, id<MTLFunction> vertexFunction, id<MTLFunction> fragmentFunction, MTLPixelFormat colorFormat, MTLPixelFormat depthFormat) {
    MTLRenderPipelineDescriptor* descriptor = [[MTLRenderPipelineDescriptor alloc] init];
    descriptor.vertexFunction = vertexFunction;
    descriptor.fragmentFunction = fragmentFunction;
    descriptor.colorAttachments[0].pixelFormat = colorFormat;
    descriptor.depthAttachmentPixelFormat = depthFormat;

    // Enable blending for transparency
    descriptor.colorAttachments[0].blendingEnabled = YES;
    descriptor.colorAttachments[0].rgbBlendOperation = MTLBlendOperationAdd;
    descriptor.colorAttachments[0].alphaBlendOperation = MTLBlendOperationAdd;
    descriptor.colorAttachments[0].sourceRGBBlendFactor = MTLBlendFactorSourceAlpha;
    descriptor.colorAttachments[0].sourceAlphaBlendFactor = MTLBlendFactorSourceAlpha;
    descriptor.colorAttachments[0].destinationRGBBlendFactor = MTLBlendFactorOneMinusSourceAlpha;
    descriptor.colorAttachments[0].destinationAlphaBlendFactor = MTLBlendFactorOneMinusSourceAlpha;

    NSError* error = nil;
    id<MTLRenderPipelineState> pipelineState = [device newRenderPipelineStateWithDescriptor:descriptor error:&error];

    [descriptor release];

    if (!CheckError(error, "CreateRenderPipelineState") || !pipelineState) {
        return nil;
    }

    return pipelineState;
}

} // namespace MetalUtils

} // namespace Graphics
} // namespace Engine
